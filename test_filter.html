<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<title>Usable Multi-Select Filter</title>
<style>
:root{
  --bg:#f7f8fa;--card:#fff;--text:#111;
  --muted:#6b7280;--border:#e5e7eb;--accent:#2563eb
}
body{font-family:system-ui,Segoe UI,Roboto,Arial,sans-serif;
  background:var(--bg);color:var(--text);margin:24px}
.toolbar{display:flex;gap:12px;flex-wrap:wrap;
  align-items:center;padding:12px;border:1px solid var(--border);
  border-radius:8px;background:white;max-width:600px}
.dropdown{position:relative;min-width:220px}
.dropdown-btn{width:100%;padding:8px 10px;border:1px solid var(--border);
  border-radius:8px;background:#fff;cursor:pointer;display:flex;
  justify-content:space-between;align-items:center}
.dropdown-list{position:absolute;top:110%;left:0;right:0;z-index:10;
  background:white;border:1px solid var(--border);border-radius:8px;
  max-height:180px;overflow-y:auto;display:none;box-shadow:0 4px 10px rgba(0,0,0,0.1)}
.dropdown-list label{display:flex;align-items:center;padding:6px 10px;
  cursor:pointer;gap:8px}
.dropdown-list label:hover{background:#f3f4f6}
.tag{display:inline-block;background:var(--accent);color:white;
  font-size:12px;padding:2px 6px;border-radius:6px;margin-right:6px}
</style>
</head>
<body>

<h2>Better Multi-Select Filter</h2>
<div class="toolbar">
  <div class="dropdown">
    <div id="dropdown-btn" class="dropdown-btn">
      <span id="dropdown-label">All Tags</span> ▼
    </div>
    <div id="dropdown-list" class="dropdown-list">
      <label><input type="checkbox" value="Easy"> 🟢 Easy</label>
      <label><input type="checkbox" value="Medium"> 🟡 Medium</label>
      <label><input type="checkbox" value="Hard"> 🔴 Hard</label>
      <label><input type="checkbox" value="🌍 High Impact"> 🌍 High Impact</label>
      <label><input type="checkbox" value="🚀 Very Innovative"> 🚀 Very Innovative</label>
    </div>
  </div>
  <button id="test-btn">Test Selection</button>
</div>

<div id="output" style="margin-top:20px;padding:10px;border:1px solid var(--border);border-radius:8px;background:white;">
  <p>Selected tags will appear here...</p>
</div>

<script>
const dropdownBtn = document.getElementById("dropdown-btn");
const dropdownList = document.getElementById("dropdown-list");
const dropdownLabel = document.getElementById("dropdown-label");
const testBtn = document.getElementById("test-btn");
const output = document.getElementById("output");

// Toggle dropdown
dropdownBtn.addEventListener("click", () => {
  dropdownList.style.display = dropdownList.style.display === "block" ? "none" : "block";
});

// Close when clicking outside
document.addEventListener("click", (e) => {
  if (!dropdownBtn.contains(e.target) && !dropdownList.contains(e.target)) {
    dropdownList.style.display = "none";
  }
});

// Update label
function updateLabel() {
  const checked = dropdownList.querySelectorAll("input:checked");
  if (checked.length === 0) {
    dropdownLabel.textContent = "All Tags";
  } else if (checked.length <= 2) {
    dropdownLabel.innerHTML = "";
    checked.forEach(c => {
      const span = document.createElement("span");
      span.className = "tag";
      span.textContent = c.value;
      dropdownLabel.appendChild(span);
    });
  } else {
    dropdownLabel.textContent = `${checked.length} selected`;
  }
}

// Update on change
dropdownList.addEventListener("change", updateLabel);

// Test button
testBtn.addEventListener("click", () => {
  const checked = Array.from(dropdownList.querySelectorAll("input:checked")).map(c => c.value);
  output.innerHTML = `<p><strong>Current selection:</strong> ${checked.length > 0 ? checked.join(", ") : "None"}</p>`;
});

// init
updateLabel();
</script>

</body>
</html>
